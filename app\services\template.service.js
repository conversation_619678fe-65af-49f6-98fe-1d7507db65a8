import * as fs from "fs";
import pool from "../config/db.js";
import ErrorHandler from "#middlewares/ErrorClass.js";
import { runQuery } from "#utils";

export const createTemplate = async (tem_name, tem_desc, export_config = null) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    // Default export config if not provided
    const defaultExportConfig = {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };

    const finalExportConfig = export_config || defaultExportConfig;

    var sql = fs.readFileSync("app/sql/createTemplate.sql").toString();
    // Update SQL to include export_config
    sql = sql.replace(
      "VALUES (?, ?)",
      "VALUES (?, ?, ?)"
    );
    sql = sql.replace(
      "(tem_name, tem_desc)",
      "(tem_name, tem_desc, export_config)"
    );

    let template = await runQuery(con, sql, [
      tem_name, 
      tem_desc, 
      JSON.stringify(finalExportConfig)
    ]);
    
    await con.commit();
    return template;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getAllTemplate = async (page, size = 10, keyword) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let template, templateCount;
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getAllTemplate.sql").toString();
      template = await runQuery(con, sql, [(page - 1) * size, size * 1]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllTemplateCount.sql")
        .toString();
      templateCount = await runQuery(con, sqlcount);
    } else {
      var sql = fs.readFileSync("app/sql/getAllTemplateKeyword.sql").toString();
      template = await runQuery(con, sql, [
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllTemplateCountKeyword.sql")
        .toString();
      templateCount = await runQuery(con, sqlcount, [
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = templateCount[0]["COUNT(*)"];
    await con.commit();
    return { template, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const getTemplate = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    // Get all template data with optimized JOIN query
    var sql = fs.readFileSync("app/sql/getTemplate.sql").toString();
    let templateData = await runQuery(con, sql, [id]);

    if (templateData.length == 0) {
      await con.commit();
      return [];
    }

    // Get conditions separately as they don't fit well in the main JOIN
    var conditionsSql = fs.readFileSync("app/sql/getTemplateConditions.sql").toString();
    let conditions = await runQuery(con, conditionsSql, [id]);

    // Process the flattened data into hierarchical structure
    let ans = {
      id: id,
      name: templateData[0]?.tem_name,
      description: templateData[0]?.tem_desc,
      status: templateData[0]?.template_status,
      price: templateData[0]?.template_price,
      groups: [],
      conditions: []
    };

    // Group the data by group_id, question_id while preserving order
    let groupsMap = new Map();
    let questionsMap = new Map();
    let groupOrder = []; // Track group order
    let questionOrder = new Map(); // Track question order per group

    for (const row of templateData) {
      if (!row.group_id) continue;

      // Process group
      if (!groupsMap.has(row.group_id)) {
        groupsMap.set(row.group_id, {
          id: row.group_id,
          name: row.group_name,
          tooltips: row.group_tooltips,
          linkedTo: row.group_answer_id ? row.group_answer_id.split(", ") : null,
          questions: []
        });
        groupOrder.push(row.group_id); // Track order
        questionOrder.set(row.group_id, []); // Initialize question order for this group
      }

      // Process question
      if (row.question_id && !questionsMap.has(row.question_id)) {
        let question = {
          id: row.question_id,
          name: row.question_name,
          description: row.question_text,
          answer_type: row.answer_type,
          required: row.required,
          tooltips: row.question_tooltips,
          selectAnswerTable: row.selectAnswerTable,
          linkedTo: row.parent_answer ? row.parent_answer.split(", ") : null,
          answers: {
            id: row.ans_id,
            name: row.answer_name,
            modal_id: row.modal_id,
            modal_type: row.modal_type,
            content: null
          }
        };

        // Handle answer content
        if (row.answer_content != null) {
          question.answers.content = row.answer_content;
        } else {
          // Collect all answers for this question
          let answersContent = [];
          for (const dataRow of templateData) {
            if (dataRow.question_id === row.question_id && dataRow.answers_id) {
              answersContent.push({
                id: dataRow.answers_id,
                name: dataRow.answers_name,
                description: dataRow.answers_text
              });
            }
          }
          question.answers.content = answersContent.length > 0 ? answersContent : null;
        }

        questionsMap.set(row.question_id, question);
        questionOrder.get(row.group_id).push(row.question_id); // Track question order
      }
    }

    // Build groups array in the correct order
    ans.groups = [];
    for (const groupId of groupOrder) {
      let group = groupsMap.get(groupId);
      // Add questions in the correct order
      group.questions = [];
      for (const questionId of questionOrder.get(groupId)) {
        if (questionsMap.has(questionId)) {
          group.questions.push(questionsMap.get(questionId));
        }
      }
      ans.groups.push(group);
    }

    // Process conditions
    for (const index in conditions) {
      let temp = JSON.parse(conditions[index]?.content);
      ans.conditions[index] = temp;
    }

    await con.commit();
    return ans;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const deleteTemplate = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let groups = await runQuery(
      con,
      "Select * from `groups` where tem_id = ?",
      [id]
    );
    for (const index in groups) {
      let questions = await runQuery(
        con,
        "Select * from `questions` where gr_id = ?",
        [groups[index]?.id]
      );
      for (const j in questions) {
        let answer = await runQuery(
          con,
          "Select * from `answer` where ques_id = ?",
          [questions[j]?.id]
        );
        let answers = await runQuery(
          con,
          "Select * from `answers` where ques_id = ?",
          [answer[0]?.ans_id]
        );
        for (const k in answers) {
          await runQuery(
            con,
            "Update `answers` set ques_id = NULL where id = ?",
            [answers[k]?.id]
          );
        }
        await runQuery(
          con,
          "update `questions` set parent_answer = NULL ,gr_id = NULL where id = ?",
          [questions[j]?.id]
        );
        await runQuery(
          con,
          "update `answer` set ques_id = NULL  where id = ?",
          [answer[0]?.id]
        );
      }
      await runQuery(
        con,
        "update `groups` set answer_id = NULL, tem_id = NULL where id = ?",
        [groups[index]?.id]
      );
    }
    await runQuery(con, "Delete from `answer` where ques_id is NULL");
    await runQuery(con, "Delete from `answers` where ques_id is NULL");
    await runQuery(con, "Delete from `questions` where gr_id is NULL");
    await runQuery(con, "Delete from `groups` where tem_id is NULL");
    await runQuery(con, "Delete from `templates` where id = ?", id);
    await runQuery(con, "Delete from `conditions` where template_id = ?", id);
    await con.commit();
    return 1;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateTemplate = async (obj, email) => {
  try {
    await deleteTemplate(obj.id);
    await createTemplateFromJson(obj, email);
    return 1;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};
export const createTemplateFromJson = async (obj, email) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let check_temp =await runQuery(con, "Select * from templates where tem_name = ?",[obj.name])
    if (check_temp.length >0){
      throw new Error('Template with the same name already exists.')
    }
    let user = await runQuery(con, "Select user_id from users where email= ?", [
      email,
    ]);
    var sql = fs.readFileSync("app/sql/createTemplateWithId.sql").toString();
    let template = await runQuery(con, sql, [
      obj.id,
      obj.name,
      obj.description,
      obj.status,
      obj.price,
      user[0]["user_id"],
    ]);
    for (const group of obj?.groups) {
      var sql = fs.readFileSync("app/sql/createGroup.sql").toString();
      await runQuery(con, sql, [
        group.id,
        group.name,
        null,
        obj.id,
        group.tooltips,
      ]);
      for (const question of group?.questions) {
        var sql = fs.readFileSync("app/sql/createQuestion.sql").toString();
        await runQuery(con, sql, [
          question.id,
          question.name,
          question.description,
          question.answer_type,
          null,
          null,
          question.required,
          question.selectAnswerTable,
          question.tooltips,
          JSON.stringify(question.files) ?? ""
        ]);
        if (question?.answers.content != null) {
          if (typeof question?.answers.content == "string") {
            await runQuery(
              con,
              "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`answer`) VALUES ( ?, ?, ?, ?, ?)",
              [
                question.answers.id,
                question.answers.name,
                question.answer_type,
                null,
                question?.answers.content,
              ]
            );
          } else {
            await runQuery(
              con,
              "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`) VALUES ( ?, ?, ?, ?)",
              [
                question.answers.id,
                question.answers.name,
                question.answer_type,
                null,
              ]
            );
            for (const answer of question?.answers.content) {
              var sql = fs.readFileSync("app/sql/createAnswer.sql").toString();
              await runQuery(con, sql, [
                answer.id,
                answer.name,
                answer.description,
                null,
              ]);
            }
          }
        } else {
          await runQuery(
            con,
            "INSERT INTO `answer` (`ans_id`, `name`, `ans_type`, `ques_id`,`modal_id`,`modal_type`) VALUES ( ?, ?, ?, ?, ?, ?)",
            [
              question.answers.id,
              question.answers.name,
              question.answer_type,
              null,
              question.answers.modal_id,
              question.answers.modal_type
            ]
          );
        }
      }
    }
    for (const group of obj?.groups) {
      await runQuery(con, "update `groups` set answer_id = ? where id = ?", [
        typeof group?.linkedTo?.join(", ") !== "undefined"
          ? group?.linkedTo?.join(", ")
          : null,
        group.id,
      ]);
      for (const question of group?.questions) {
        await runQuery(
          con,
          "update `questions` set parent_answer = ?,gr_id = ? where id = ?",
          [
            typeof question?.linkedTo?.join(", ") !== "undefined"
              ? question?.linkedTo?.join(", ")
              : null,
            group.id,
            question.id,
          ]
        );
        await runQuery(
          con,
          "update `answer` set ques_id = ? where ans_id = ?",
          [question.id, question.answers.id]
        );
        if (question?.answers.content != null)
          if (typeof question?.answers.content != "string") {
            for (const answers of question?.answers.content) {
              await runQuery(
                con,
                "update `answers` set ques_id = ? where id = ?",
                [question.answers.id, answers.id]
              );
            }
          }
      }
    }
    if (obj?.conditions) {
      for (const conditions of obj?.conditions) {
        await runQuery(
          con,
          "INSERT INTO `conditions`(template_id,content) VALUES (?,?)",
          [obj?.id, JSON.stringify(conditions)]
        );
      }
    }

    await con.commit();
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const updateTemplateStatus = async (obj, email) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(
      con,
      "Update `templates` set status = ? , update_at = Now() , update_by = ?  where id = ?",
      [obj.status, email, obj.id]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const createTemplateAnswer = async (obj) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(
      con,
      "Insert into answers_template (content,name,ans_type,status,created_at) VALUES (?,?,?,1, NOW())",
      [JSON.stringify({ answers: obj.content }), obj.name, obj.ans_type]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getAllTemplateAnswer = async (page, size, keyword, status) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();
    let temp_ans, temp_count;
    if (keyword == null) {
      var sql = fs.readFileSync("app/sql/getAllTemplateAnswer.sql").toString();
      temp_ans = await runQuery(con, sql, [
        status,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllTemplateAnswerCount.sql")
        .toString();
      temp_count = await runQuery(con, sqlcount, [status]);
    } else {
      var sql = fs
        .readFileSync("app/sql/getAllTemplateAnswerKeyword.sql")
        .toString();
      temp_ans = await runQuery(con, sql, [
        status,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        (page - 1) * size,
        size * 1,
      ]);
      var sqlcount = fs
        .readFileSync("app/sql/getAllTemplateAnswerCountKeyword.sql")
        .toString();
      temp_count = await runQuery(con, sqlcount, [
        status,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
        `%${keyword}%`,
      ]);
    }
    let count = temp_count[0]["COUNT(*)"];
    await con.commit();
    return { temp_ans, count };
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getTemplateAnswer = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    let ans = await runQuery(
      con,
      "Select * from answers_template where id = ?",
      [id]
    );
    await con.commit();
    return ans;
  } catch (error) {
    await con.rollback();
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const deleteTemplateAnswer = async (id) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(con, "Delete from answers_template where id = ?", [id]);
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateTemplateAnswerStatus = async (obj) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(
      con,
      "Update answers_template SET status = ?, update_at=NOW() where id = ?",
      [typeof obj?.status !== "undefined" ? obj?.status : 1, obj.id]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();

    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};
export const updateTemplateAnswer = async (obj) => {
  let con = await pool.getConnection();

  try {
    await con.beginTransaction();

    await runQuery(
      con,
      "Update answers_template set content = ?, name = ?, ans_type = ?, status = ?,update_at = NOW() where id = ?",
      [
        JSON.stringify({ answers: obj.content }),
        obj.name,
        obj.ans_type,
        typeof obj?.status !== "undefined" ? obj?.status : 1,
        obj.id,
      ]
    );
    await con.commit();
    return 1;
  } catch (error) {
    console.log(error);
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

function extractQuestionIds(data) {
  let questionIds = [];
  function traverse(obj) {
    if (typeof obj === "object" && obj !== null) {
      if (obj.hasOwnProperty("questionId")) {
        questionIds.push(obj["questionId"]);
      }
      for (let key in obj) {
        traverse(obj[key]);
      }
    }
  }
  traverse(data);
  return questionIds;
}
function findUniqueGroups(questionIds, groupArray) {
  let uniqueGroups = new Set();
  for (let questionId of questionIds) {
    for (let group of groupArray) {
      for (let question of group.questions) {
        if (question.id === questionId) {
          uniqueGroups.add(group.id);
        }
      }
    }
  }
  return Array.from(uniqueGroups);
}
function mergeListsUnique(list1, list2) {
  let mergedSet = new Set([...list1, ...list2]);
  return Array.from(mergedSet);
}

function alterValueById(idToMatch, newValue, jsonData) {
  for (let group of jsonData.groups) {
    if (group.id === idToMatch) {
      if (newValue == null) return;
      if (group.linkedTo != null) {
        group.linkedTo = mergeListsUnique(group.linkedTo, newValue);
      } else group.linkedTo = newValue;
      return;
    }
    for (let question of group.questions) {
      if (question.id === idToMatch) {
        if (newValue == null) return;
        if (question.linkedTo != null) {
          question.linkedTo = mergeListsUnique(question.linkedTo, newValue);
        } else question.linkedTo = newValue;
      }
    }
  }
}
function findItemTypeById(idToFind, jsonData) {
  // Check if the ID matches any group IDs
  for (let group of jsonData.groups) {
    if (group.id === idToFind) {
      return "group"; // If matched, it's a group
    }
  }

  // If not a group, check if the ID matches any question IDs within groups
  for (let group of jsonData.groups) {
    for (let question of group.questions) {
      if (question.id === idToFind) {
        return "question"; // If matched, it's a question
      }
    }
  }

  // If not found, return null
  return null;
}
function removeElement(array, elem) {
  var index = array.indexOf(elem);
  if (index > -1) {
    array.splice(index, 1);
  }
}
function findQuestionsInGroup(groupId, questionIds, jsonData) {
  let group = jsonData.groups.find((group) => group.id === groupId);
  if (!group) {
    console.log("Group not found.");
    return [];
  }

  let groupQuestionIds = group.questions.map((question) => question.id);
  let questionsInGroup = questionIds.filter((questionId) =>
    groupQuestionIds.includes(questionId)
  );
  return questionsInGroup;
}

function hasQuestionIdInList(jsonData, targetQuestionId) {
  if (!jsonData.list || !Array.isArray(jsonData.list)) {
    return false;
  }

  for (let item of jsonData.list) {
    if (item.questionId === targetQuestionId) {
      return true;
    }
    // If the item is a group, recursively check its children
    if (item.type === "group" && item.children) {
      if (hasQuestionIdInList({ list: item.children }, targetQuestionId)) {
        return true;
      }
    }
  }

  return false;
}
function findAnswerIdsByQuestionIds(questionIds, jsonData) {
  let answerIds = [];

  // Iterate through groups
  for (let group of jsonData.groups) {
    // Iterate through questions in the group
    for (let question of group.questions) {
      // Check if the question ID is in the list
      if (questionIds.includes(question.id)) {
        // If yes, collect answer IDs
        if (question.answers) {
          answerIds.push(question.answers.id);
        }
      }
    }
  }

  return answerIds;
}
export const createLink = async (obj) => {
  try {
    for (const groups of obj?.groups) {
      groups.linkedTo = null;
      for (const questions of groups?.questions) {
        questions.linkedTo = null;
      }
    }
    for (const conditions of obj?.conditions) {
      let temp = extractQuestionIds(conditions);
      // let final = findUniqueGroups(temp, obj?.groups);
      // let con = findItemTypeById(conditions?.linkTo, obj);
      // if (con == "question") {
      //   let group_question = findUniqueGroups(
      //     [conditions?.linkTo],
      //     obj?.groups
      //   );
      //   if (final.length == 1 && final[0] == group_question[0]) {
      let final = findAnswerIdsByQuestionIds(temp, obj);
      // } else {
      //   removeElement(final, group_question[0]);
      //   alterValueById(group_question[0], final, obj);
      //   let questions = findQuestionsInGroup(group_question[0], temp, obj);
      //   if (questions.length == 0) {
      //     final = null;
      //   } else {
      //     final = questions;
      //   }
      // }
      // }
      alterValueById(conditions.linkTo, final, obj);
    }
    return obj;
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};
export const modifyQuestion = async (obj) => {
  try {
    if (obj.change.questionId) {
      try {
        if (obj.change.mode == "delete") {
          let i = 0;
          while (i < obj?.conditions.length) {
            if (
              obj.conditions[i]?.linkTo == obj.change.questionId ||
              hasQuestionIdInList(obj.conditions[i], obj.change.questionId)
            ) {
              obj.conditions.splice(i, 1);
            } else {
              i += 1;
            }
          }
        } else {
          let i = 0;
          while (i < obj?.conditions.length) {
            if (hasQuestionIdInList(obj.conditions[i], obj.change.questionId)) {
              obj.conditions.splice(i, 1);
            } else {
              i += 1;
            }
          }
        }
        for (let group of obj.groups) {
          for (let question of group.questions) {
            if (question.id == obj.change.questionId) {
              if (obj.change.mode == "delete") {
                var index = group.questions.indexOf(question);
                if (index !== -1) {
                  group.questions.splice(index, 1);
                }
              } else {
                question.name = obj.change.name;
                question.description = obj.change.description;
                question.answer_type = obj.change.answer_type;
                question.answers = obj.change.answers;
                question.required = obj.change.required;
                question.selectAnswerTable = obj.change.selectAnswerTable;
                question.tooltips = obj.change.tooltips;
              }
            }
          }
        }
        delete obj.change;

        return await createLink(obj);
      } catch (error) {
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    } else {
      try {
        for (let group of obj.groups) {
          if (group.id == obj.change.groupId) {
            for (let question of group.questions) {
              let i = 0;
              while (i < obj?.conditions.length) {
                if (
                  obj.conditions[i]?.linkTo == question.id ||
                  hasQuestionIdInList(obj.conditions[i], question.id)
                ) {
                  obj.conditions.splice(i, 1);
                } else {
                  i += 1;
                }
              }
            }
            let i = 0;
            while (i < obj?.conditions.length) {
              if (
                obj.conditions[i]?.linkTo == group.id ||
                hasQuestionIdInList(obj.conditions[i], group.id)
              ) {
                obj.conditions.splice(i, 1);
              } else {
                i += 1;
              }
            }
            var index = obj.groups.indexOf(group);
            console.log(index);
            if (index !== -1) {
              obj.groups.splice(index, 1);
            }
          }
        }
        delete obj.change;
        return await createLink(obj);
      } catch (error) {
        console.log(error);
        throw ErrorHandler.badRequestError(error.message);
      }
    }
  } catch (error) {
    console.log(error);
    throw ErrorHandler.badRequestError(error.message);
  }
};

export const updateTemplateExportConfig = async (templateId, exportConfig) => {
  const con = await pool.getConnection();
  try {
    await con.beginTransaction();
    
    await runQuery(con, 
      "UPDATE templates SET export_config = ? WHERE id = ?",
      [JSON.stringify(exportConfig), templateId]
    );
    
    await con.commit();
    return { success: true };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

export const getTemplateExportConfig = async (templateId) => {
  const con = await pool.getConnection();
  try {
    const result = await runQuery(con,
      "SELECT export_config FROM templates WHERE id = ?",
      [templateId]
    );

    if (result.length > 0 && result[0].export_config) {
      return typeof result[0].export_config === 'string'
        ? JSON.parse(result[0].export_config)
        : result[0].export_config;
    }

    return {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };
  } finally {
    con.destroy();
  }
};

export const getTemplateWithExportConfig = async (templateId) => {
  const con = await pool.getConnection();
  try {
    await con.beginTransaction();

    // Get basic template info with export config
    const templateResult = await runQuery(con,
      "SELECT id, tem_name, tem_desc, status, price, export_config FROM templates WHERE id = ?",
      [templateId]
    );

    if (templateResult.length === 0) {
      await con.commit();
      return null;
    }

    const template = templateResult[0];

    // Parse export config
    let exportConfig = {
      pms_enabled: true,
      data_enabled: true,
      form_enabled: false,
      form_names: ''
    };

    if (template.export_config) {
      exportConfig = typeof template.export_config === 'string'
        ? JSON.parse(template.export_config)
        : template.export_config;
    }

    await con.commit();

    return {
      id: template.id,
      name: template.tem_name,
      description: template.tem_desc,
      status: template.status,
      price: template.price,
      export_config: exportConfig
    };
  } catch (error) {
    await con.rollback();
    throw ErrorHandler.badRequestError(error.message);
  } finally {
    con.destroy();
  }
};

