-- Set default export configuration for templates table
ALTER TABLE templates 
ALTER COLUMN export_config SET DEFAULT JSON_OBJECT(
  'pms_enabled', true,
  'data_enabled', true, 
  'form_enabled', false,
  'form_names', ''
);

-- Set default export configuration for questionaires table  
ALTER TABLE questionaires 
ALTER COLUMN export_config SET DEFAULT JSON_OBJECT(
  'pms_enabled', true,
  'data_enabled', true, 
  'form_enabled', false,
  'form_names', ''
);

-- Update any existing NULL values to use the default configuration
UPDATE templates 
SET export_config = JSON_OBJECT(
  'pms_enabled', true,
  'data_enabled', true, 
  'form_enabled', false,
  'form_names', ''
) 
WHERE export_config IS NULL;

UPDATE questionaires 
SET export_config = JSON_OBJECT(
  'pms_enabled', true,
  'data_enabled', true, 
  'form_enabled', false,
  'form_names', ''
) 
WHERE export_config IS NULL;
