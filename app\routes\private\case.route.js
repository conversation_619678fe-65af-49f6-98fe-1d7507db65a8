import express from "express";
import {
  getAllCase,
  createCase,
  getCase,
  deleteCase,
  updateAnswer,
  submitCase,
  updateCaseStatus,
  getCaseSumByUserId,
  getCaseByQuestionaireId,
  getCaseByUserId,
  callMe,
  downloadPDF,
  grantPermission,
  denyPermission,
  getAllGranted,
  getModalAnswer,
  getCaseIdFromPMS,
  getDetailCaseAnswerById,
  exportPDFToPMS,
  exportDataToPMS,
  createCaseFromPMS,
  getPartiesForAssignQtn,
  updateEmailorPhoneParty,
  assignQtnToParty,
  checkInfoParty,
  updateAnswerSubmitted,
  uploadDocument,
  downloadDocument,
  updateStatusExported,
  getCaseData,
  downloadAfterSubmit,
  generateSpreadsheet,
  exportToForm,
  getCaseWithExportConfig,
} from "#controllers/case.controller.js";
import { upload } from "#middlewares/upload_file.js";

/**
 * @swagger
 * components:
 *   schemas:
 *     Case:
 *       type: object
 *       properties:
 *         case_id:
 *           type: string
 *           description: Unique identifier for the case
 *         case_name:
 *           type: string
 *           description: Name of the case
 *         status:
 *           type: integer
 *           description: Current status of the case
 *         user_id:
 *           type: string
 *           description: User associated with the case
 *         lf_id:
 *           type: string
 *           description: Law firm identifier
 *         desc:
 *           type: string
 *           description: Case description
 *
 *     CaseResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: false
 *         message:
 *           type: string
 *           example: Permission denied!
 */

/**
 * @swagger
 * tags:
 *   name: Cases
 *   description: Case management endpoints
 */

const caseRoutes = express.Router();

/**
 * @swagger
 * /private/case/createCase:
 *   post:
 *     summary: Create a new case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_name
 *               - cases
 *               - qtn_id
 *             properties:
 *               case_name:
 *                 type: string
 *               cases:
 *                 type: array
 *               qtn_id:
 *                 type: string
 *               user_id:
 *                 type: string
 *               case_id_pms:
 *                 type: string
 *               desc:
 *                 type: string
 *               token_:
 *                 type: string
 *     responses:
 *       200:
 *         description: Case created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 case_id:
 *                   type: string
 *       400:
 *         description: Invalid request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
caseRoutes.route("/createCase").post(createCase);

/**
 * @swagger
 * /private/case/getAllCase:
 *   post:
 *     summary: Get all cases with filtering and pagination
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - page
 *               - size
 *             properties:
 *               page:
 *                 type: number
 *               size:
 *                 type: number
 *               keyword:
 *                 type: string
 *               status:
 *                 type: number
 *               user_id:
 *                 type: string
 *               lf_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Cases retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CaseResponse'
 */
caseRoutes.route("/getAllCase").post(getAllCase);

/**
 * @swagger
 * /private/case/getCase:
 *   post:
 *     summary: Get case details by ID
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Case details retrieved
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CaseResponse'
 */
caseRoutes.route("/getCase").post(getCase);

/**
 * @swagger
 * /private/case/deleteCase:
 *   post:
 *     summary: Delete a case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Case deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/CaseResponse'
 */
caseRoutes.route("/deleteCase").post(deleteCase);

/**
 * @swagger
 * /private/case/updateAnswer:
 *   post:
 *     summary: Update case answers
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - answer
 *             properties:
 *               id:
 *                 type: string
 *               answer:
 *                 type: array
 *     responses:
 *       200:
 *         description: Answers updated successfully
 */
caseRoutes.route("/updateAnswer").post(updateAnswer);

/**
 * @swagger
 * /private/case/updateAnswerSubmitted:
 *   post:
 *     summary: Update submitted answers
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 */
caseRoutes.route("/updateAnswerSubmitted").post(updateAnswerSubmitted);

/**
 * @swagger
 * /private/case/updateCaseStatus:
 *   post:
 *     summary: Update case status
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *               - id
 *             properties:
 *               status:
 *                 type: number
 *               id:
 *                 type: string
 */
caseRoutes.route("/updateCaseStatus").post(updateCaseStatus);

/**
 * @swagger
 * /private/case/submitCase:
 *   post:
 *     summary: Submit a case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *             properties:
 *               id:
 *                 type: string
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Case submitted successfully
 */
caseRoutes.route("/submitCase").post(submitCase);

/**
 * @swagger
 * /private/case/getCaseSumByUserId:
 *   get:
 *     summary: Get case summary by user ID with pagination
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: number
 *         required: true
 *       - in: query
 *         name: size
 *         schema:
 *           type: number
 *         required: true
 *       - in: query
 *         name: keyword
 *         schema:
 *           type: string
 */
caseRoutes.route("/getCaseSumByUserId").get(getCaseSumByUserId);

/**
 * @swagger
 * /private/case/download:
 *   post:
 *     summary: Download case PDF
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: Returns PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *   get:
 *     summary: Download case PDF after submission
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: case_id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Returns PDF file
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 */
caseRoutes.route("/download").post(downloadPDF);
caseRoutes.route("/download").get(downloadAfterSubmit);

/**
 * @swagger
 * /private/case/grantPermission:
 *   post:
 *     summary: Grant case access permission to a user
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - email
 *             properties:
 *               case_id:
 *                 type: string
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Permission granted successfully
 */
caseRoutes.route("/grantPermission").post(grantPermission);

/**
 * @swagger
 * /private/case/denyPermission:
 *   post:
 *     summary: Revoke case access permission from a user
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - email
 *             properties:
 *               case_id:
 *                 type: string
 *               email:
 *                 type: string
 */
caseRoutes.route("/denyPermission").post(denyPermission);

/**
 * @swagger
 * /private/case/uploadDocument:
 *   post:
 *     summary: Upload a document for a case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *               case_id:
 *                 type: string
 */
caseRoutes.route("/uploadDocument").post(upload.single("file"), uploadDocument);

/**
 * @swagger
 * /private/case/downloadDocument:
 *   get:
 *     summary: Download a case document
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: file
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: case_id
 *         required: true
 *         schema:
 *           type: string
 */
caseRoutes.route("/downloadDocument").get(downloadDocument);

/**
 * @swagger
 * /private/case/exportPDFToPMS:
 *   post:
 *     summary: Export case PDF to PMS
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - lf_id
 *             properties:
 *               id:
 *                 type: string
 *               case_id_pms:
 *                 type: string
 *               lf_id:
 *                 type: string
 */
caseRoutes.route("/exportPDFToPMS").post(exportPDFToPMS);

/**
 * @swagger
 * /private/case/exportDataToPMS:
 *   post:
 *     summary: Export case data to PMS
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - lf_id
 *             properties:
 *               id:
 *                 type: string
 *               case_id_pms:
 *                 type: string
 *               lf_id:
 *                 type: string
 *               qtn_id:
 *                 type: string
 */
caseRoutes.route("/exportDataToPMS").post(exportDataToPMS);

/**
 * @swagger
 * /private/case/generateSpreadsheet:
 *   post:
 *     summary: Generate an Excel spreadsheet for a case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - check
 *             properties:
 *               case_id:
 *                 type: string
 *               check:
 *                 type: number
 *                 description: Type of spreadsheet to generate (1 or 2)
 */
caseRoutes.route("/generateSpreadsheet").post(generateSpreadsheet);

/**
 * @swagger
 * /private/case/getCaseByQuestionaireId:
 *   post:
 *     summary: Get cases by questionnaire ID
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - qtn_id
 *             properties:
 *               qtn_id:
 *                 type: string
 */
caseRoutes.route("/getCaseByQuestionaireId").post(getCaseByQuestionaireId);

/**
 * @swagger
 * /private/case/getCaseByUserId:
 *   post:
 *     summary: Get cases by user ID
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 */
caseRoutes.route("/getCaseByUserId").post(getCaseByUserId);

/**
 * @swagger
 * /private/case/callMe:
 *   post:
 *     summary: Request a callback for a case
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 *               phone:
 *                 type: string
 */
caseRoutes.route("/callMe").post(callMe);

/**
 * @swagger
 * /private/case/getAllGranted:
 *   post:
 *     summary: Get all cases with granted access
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               page:
 *                 type: number
 *               size:
 *                 type: number
 */
caseRoutes.route("/getAllGranted").post(getAllGranted);

/**
 * @swagger
 * /private/case/getModalAnswer:
 *   post:
 *     summary: Get modal answer details
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 */
caseRoutes.route("/getModalAnswer").post(getModalAnswer);

/**
 * @swagger
 * /private/case/getClientPMS:
 *   get:
 *     summary: Get case ID from PMS
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: client_id
 *         required: true
 *         schema:
 *           type: string
 */
caseRoutes.route("/getClientPMS").get(getCaseIdFromPMS);

/**
 * @swagger
 * /private/case/getDetailCase:
 *   post:
 *     summary: Get detailed case answers by ID
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 */
caseRoutes.route("/getDetailCase").post(getDetailCaseAnswerById);

/**
 * @swagger
 * /private/case/createCaseFromPMS:
 *   post:
 *     summary: Create a new case from PMS data
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id_pms
 *               - lf_id
 *             properties:
 *               case_id_pms:
 *                 type: string
 *               lf_id:
 *                 type: string
 */
caseRoutes.route("/createCaseFromPMS").post(createCaseFromPMS);

/**
 * @swagger
 * /private/case/getPartiesForAssignQtn:
 *   post:
 *     summary: Get parties available for questionnaire assignment
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *             properties:
 *               case_id:
 *                 type: string
 */
caseRoutes.route("/getPartiesForAssignQtn").post(getPartiesForAssignQtn);

/**
 * @swagger
 * /private/case/updateEmailorPhoneParty:
 *   post:
 *     summary: Update party email or phone number
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - party_id
 *             properties:
 *               case_id:
 *                 type: string
 *               party_id:
 *                 type: string
 *               email:
 *                 type: string
 *               phone:
 *                 type: string
 */
caseRoutes.route("/updateEmailorPhoneParty").post(updateEmailorPhoneParty);

/**
 * @swagger
 * /private/case/assignQtnToParty:
 *   post:
 *     summary: Assign questionnaire to a party
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - party_id
 *               - qtn_id
 *             properties:
 *               case_id:
 *                 type: string
 *               party_id:
 *                 type: string
 *               qtn_id:
 *                 type: string
 */
caseRoutes.route("/assignQtnToParty").post(assignQtnToParty);

/**
 * @swagger
 * /private/case/checkInfoParty:
 *   post:
 *     summary: Check party information
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - party_id
 *             properties:
 *               case_id:
 *                 type: string
 *               party_id:
 *                 type: string
 */
caseRoutes.route("/checkInfoParty").post(checkInfoParty);

/**
 * @swagger
 * /private/case/updateStatusExported:
 *   post:
 *     summary: Update case export status
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - case_id
 *               - status
 *             properties:
 *               case_id:
 *                 type: string
 *               status:
 *                 type: number
 */
caseRoutes.route("/updateStatusExported").post(updateStatusExported);
caseRoutes.route("/getCaseData").post(getCaseData);

/**
 * @swagger
 * /private/case/exportToForm:
 *   post:
 *     summary: Export case data to forms via PMS
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - lf_id
 *             properties:
 *               id:
 *                 type: string
 *               case_id_pms:
 *                 type: string
 *               lf_id:
 *                 type: string
 *               qtn_id:
 *                 type: string
 */
caseRoutes.route("/exportToForm").post(exportToForm);

/**
 * @swagger
 * /private/case/{id}/export-config:
 *   get:
 *     summary: Get case with export configuration
 *     tags: [Cases]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Case ID
 *     responses:
 *       200:
 *         description: Case data with export configuration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     export_config:
 *                       type: object
 *                       properties:
 *                         pms_enabled:
 *                           type: boolean
 *                         data_enabled:
 *                           type: boolean
 *                         form_enabled:
 *                           type: boolean
 *                         form_names:
 *                           type: string
 */
caseRoutes.get("/:id/export-config", getCaseWithExportConfig);

export default caseRoutes;


