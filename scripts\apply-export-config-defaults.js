/**
 * <PERSON><PERSON><PERSON> to apply export configuration defaults to database schema
 * This sets default values for export_config columns in templates and questionaires tables
 */

import fs from 'fs';
import path from 'path';
import pool from '../app/config/db.js';

async function applyExportConfigDefaults() {
  const con = await pool.getConnection();
  
  try {
    console.log('🔄 Applying export configuration defaults...');
    
    // Read the migration SQL
    const migrationPath = path.join(process.cwd(), 'app/migrations/007_set_export_config_defaults.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      console.log(`📝 Executing: ${statement.substring(0, 50)}...`);
      await con.query(statement);
    }
    
    console.log('✅ Export configuration defaults applied successfully!');
    console.log('');
    console.log('📋 Summary of changes:');
    console.log('  • Set default export_config for templates table');
    console.log('  • Set default export_config for questionaires table');
    console.log('  • Updated existing NULL values with default configuration');
    console.log('');
    console.log('🎯 Default configuration:');
    console.log('  • pms_enabled: true');
    console.log('  • data_enabled: true');
    console.log('  • form_enabled: false');
    console.log('  • form_names: ""');
    
  } catch (error) {
    console.error('❌ Error applying export configuration defaults:', error);
    throw error;
  } finally {
    con.destroy();
    process.exit(0);
  }
}

// Run the script
applyExportConfigDefaults().catch(error => {
  console.error('💥 Script failed:', error);
  process.exit(1);
});
