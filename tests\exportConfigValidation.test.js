/**
 * Test suite for export configuration validation
 * Tests that export buttons are properly controlled by template-level configuration
 */

const request = require('supertest');
const app = require('../index.js');

describe('Export Configuration Validation', () => {
  let authToken;
  let testCaseId;
  let testTemplateId;
  let testQuestionnaireId;

  beforeAll(async () => {
    // This would need to be set up with actual test data
    // For now, this is a template for testing
    console.log('Export configuration tests require test data setup');
  });

  describe('Template Export Configuration', () => {
    test('should update template export configuration', async () => {
      if (!testTemplateId) {
        console.log('Skipping test - no test template ID available');
        return;
      }

      const exportConfig = {
        pms_enabled: true,
        data_enabled: false,
        form_enabled: true,
        form_names: 'form1,form2,form3'
      };

      const response = await request(app)
        .put(`/private/template/${testTemplateId}/export-config`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(exportConfig);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('should get template export configuration', async () => {
      if (!testTemplateId) {
        console.log('Skipping test - no test template ID available');
        return;
      }

      const response = await request(app)
        .get(`/private/template/${testTemplateId}/export-config`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('pms_enabled');
      expect(response.body.data).toHaveProperty('data_enabled');
      expect(response.body.data).toHaveProperty('form_enabled');
      expect(response.body.data).toHaveProperty('form_names');
    });
  });

  describe('Export Validation', () => {
    test('should reject PMS export when disabled', async () => {
      if (!testCaseId) {
        console.log('Skipping test - no test case ID available');
        return;
      }

      // First disable PMS export for the template
      // Then try to export - should fail

      const response = await request(app)
        .post('/private/case/exportPDFToPMS')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          id: testCaseId,
          lf_id: '1'
        });

      // This test would need the template to have PMS disabled
      // expect(response.status).toBe(400);
      // expect(response.body.message).toContain('PMS export not enabled');
    });

    test('should reject Data export when disabled', async () => {
      if (!testCaseId) {
        console.log('Skipping test - no test case ID available');
        return;
      }

      const response = await request(app)
        .post('/private/case/exportDataToPMS')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          id: testCaseId,
          lf_id: '1'
        });

      // This test would need the template to have Data disabled
      // expect(response.status).toBe(400);
      // expect(response.body.message).toContain('Data export not enabled');
    });

    test('should reject Form export when disabled', async () => {
      if (!testCaseId) {
        console.log('Skipping test - no test case ID available');
        return;
      }

      const response = await request(app)
        .post('/private/case/exportToForm')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          id: testCaseId,
          lf_id: '1'
        });

      // This test would need the template to have Form disabled
      // expect(response.status).toBe(400);
      // expect(response.body.message).toContain('Form export not enabled');
    });

    test('should reject Form export when no form names configured', async () => {
      if (!testCaseId) {
        console.log('Skipping test - no test case ID available');
        return;
      }

      // This test assumes form export is enabled but no form names are set
      const response = await request(app)
        .post('/private/case/exportToForm')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          id: testCaseId,
          lf_id: '1'
        });

      // expect(response.status).toBe(400);
      // expect(response.body.message).toContain('No form names configured');
    });
  });

  describe('Configuration Inheritance', () => {
    test('questionnaire should inherit template export config', async () => {
      if (!testQuestionnaireId) {
        console.log('Skipping test - no test questionnaire ID available');
        return;
      }

      const response = await request(app)
        .post(`/private/questionaire/${testQuestionnaireId}/inherit-template-config`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    test('case should use questionnaire export config', async () => {
      if (!testCaseId) {
        console.log('Skipping test - no test case ID available');
        return;
      }

      const response = await request(app)
        .get(`/private/case/${testCaseId}/export-config`)
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toHaveProperty('export_config');
    });
  });
});

// Helper function to set up test data
function setupTestData() {
  // This would create test templates, questionnaires, and cases
  // with known export configurations for testing
  console.log('Test data setup would go here');
}
